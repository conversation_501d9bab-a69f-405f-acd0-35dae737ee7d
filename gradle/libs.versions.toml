[versions]
spring-boot = "3.4.5"
spring-framework = "6.2.2"
spring-ai = "1.0.0"
spring-dependency-management = "1.1.6"
spotless = "6.25.0"
lombok = "1.18.36"
hutool = "5.8.29"
mybatis-plus = "3.5.12"
log4j2 = "2.24.3"
guava = "32.1.3-jre"
commons-lang3 = "3.17.0"
commons-text = "1.12.0"
commons-collections4 = "4.4"
commons-compress = "1.26.0"
commons-io = "2.18.0"
commons-codec = "1.17.2"
redisson = "3.37.0"
caffeine = "3.1.8"
fastjson = "2.0.53"
poi = "5.3.0"
ooxml-schemas = "1.4"
knife4j = "4.6.0"
jjwt = "0.12.6"
velocity = "2.4.1"
velocity-tools = "3.1"
freemarker = "2.3.33"
h2 = "2.3.232"
postgresql = "42.7.3"
druid = "1.2.24"
fast-excel = "1.2.0"
ip2region = "2.7.0"
sa-token = "1.40.0"
aliyun-oss = "3.17.4"
jsoup = "1.18.1"
concurrent-linked-hashmap = "1.4.2"
spring-security = "6.4.2"
hibernate-validator = "8.0.1.Final"
reflections = "0.10.2"
tika = "2.9.1"
bouncycastle = "1.78.1"

[libraries]
# BOM (Bill of Materials) - 统一版本管理
spring-boot-bom = { module = "org.springframework.boot:spring-boot-dependencies", version.ref = "spring-boot" }
spring-framework-bom = { module = "org.springframework:spring-framework-bom", version.ref = "spring-framework" }
spring-ai-bom = { module = "org.springframework.ai:spring-ai-bom", version.ref = "spring-ai" }

# gradle plugins
gradle-spring-boot = { module = "org.springframework.boot:spring-boot-gradle-plugin", version.ref = "spring-boot"}
gradle-spring-dependency-management = { module = "io.spring.gradle:dependency-management-plugin", version.ref = "spring-dependency-management"}
gradle-spotless = { module = "com.diffplug.spotless:spotless-plugin-gradle", version.ref = "spotless"}

# app libraries
spring-boot-starter-web = { module = "org.springframework.boot:spring-boot-starter-web", version.ref = "spring-boot" }
spring-boot-starter-validation = { module = "org.springframework.boot:spring-boot-starter-validation", version.ref = "spring-boot" }
spring-boot-starter-aop = { module = "org.springframework.boot:spring-boot-starter-aop", version.ref = "spring-boot" }
spring-boot-starter-cache = { module = "org.springframework.boot:spring-boot-starter-cache", version.ref = "spring-boot" }
spring-boot-starter-mail = { module = "org.springframework.boot:spring-boot-starter-mail", version.ref = "spring-boot" }
spring-boot-starter-actuator = { module = "org.springframework.boot:spring-boot-starter-actuator", version.ref = "spring-boot" }
spring-boot-starter-data-redis = { module = "org.springframework.boot:spring-boot-starter-data-redis", version.ref = "spring-boot" }
spring-boot-starter-testing = { module = "org.springframework.boot:spring-boot-starter-test", version.ref = "spring-boot" }

# Spring AI dependencies - 版本由 spring-ai-bom 管理
spring-ai-openai = { module = "org.springframework.ai:spring-ai-openai" }
spring-ai-core = { module = "org.springframework.ai:spring-ai-core" }
spring-ai-spring-boot-autoconfigure = { module = "org.springframework.ai:spring-ai-spring-boot-autoconfigure" }

lombok = { module = "org.projectlombok:lombok", version.ref = "lombok" }
hutool = { module = "cn.hutool:hutool-all", version.ref = "hutool" }
mybatis-plus-spring-boot3-starter = { module = "com.baomidou:mybatis-plus-spring-boot3-starter", version.ref = "mybatis-plus" }
mybatis-plus-jsqlparser = { module = "com.baomidou:mybatis-plus-jsqlparser", version.ref = "mybatis-plus" }
mybatis-plus-extension = { module = "com.baomidou:mybatis-plus-extension", version.ref = "mybatis-plus" }
log4j-slf4j2-impl = { module = "org.apache.logging.log4j:log4j-slf4j2-impl", version.ref = "log4j2" }
log4j-core = { module = "org.apache.logging.log4j:log4j-core", version.ref = "log4j2" }
log4j-api = { module = "org.apache.logging.log4j:log4j-api", version.ref = "log4j2" }
guava = { module = "com.google.guava:guava", version.ref = "guava" }
commons-lang3 = { module = "org.apache.commons:commons-lang3", version.ref = "commons-lang3" }
commons-text = { module = "org.apache.commons:commons-text", version.ref = "commons-text" }
commons-collections4 = { module = "org.apache.commons:commons-collections4", version.ref = "commons-collections4" }
commons-compress = { module = "org.apache.commons:commons-compress", version.ref = "commons-compress" }
commons-io = { module = "commons-io:commons-io", version.ref = "commons-io" }
commons-codec = { module = "commons-codec:commons-codec", version.ref = "commons-codec" }
redisson-spring-boot-starter = { module = "org.redisson:redisson-spring-boot-starter", version.ref = "redisson" }
caffeine = { module = "com.github.ben-manes.caffeine:caffeine", version.ref = "caffeine" }
fastjson = { module = "com.alibaba:fastjson", version.ref = "fastjson" }
poi = { module = "org.apache.poi:poi", version.ref = "poi" }
poi-ooxml = { module = "org.apache.poi:poi-ooxml", version.ref = "poi" }
poi-scratchpad = { module = "org.apache.poi:poi-scratchpad", version.ref = "poi" }
ooxml-schemas = { module = "org.apache.poi:ooxml-schemas", version.ref = "ooxml-schemas" }
knife4j-openapi3-jakarta-spring-boot-starter = { module = "com.github.xingfudeshi:knife4j-openapi3-jakarta-spring-boot-starter", version.ref = "knife4j" }
jjwt-api = { module = "io.jsonwebtoken:jjwt-api", version.ref = "jjwt" }
jjwt-impl = { module = "io.jsonwebtoken:jjwt-impl", version.ref = "jjwt" }
jjwt-jackson = { module = "io.jsonwebtoken:jjwt-jackson", version.ref = "jjwt" }
velocity-engine-core = { module = "org.apache.velocity:velocity-engine-core", version.ref = "velocity" }
velocity-tools-generic = { module = "org.apache.velocity.tools:velocity-tools-generic", version.ref = "velocity-tools" }
freemarker = { module = "org.freemarker:freemarker", version.ref = "freemarker" }
h2 = { module = "com.h2database:h2", version.ref = "h2" }
postgresql = { module = "org.postgresql:postgresql", version.ref = "postgresql" }
druid-spring-boot3-starter = { module = "com.alibaba:druid-spring-boot-3-starter", version.ref = "druid" }
fast-excel = { module = "cn.idev.excel:fastexcel", version.ref = "fast-excel" }
ip2region = { module = "org.lionsoul:ip2region", version.ref = "ip2region" }
sa-token-spring-boot3-starter = { module = "cn.dev33:sa-token-spring-boot3-starter", version.ref = "sa-token" }
aliyun-oss = { module = "com.aliyun.oss:aliyun-sdk-oss", version.ref = "aliyun-oss" }
jsoup = { module = "org.jsoup:jsoup", version.ref = "jsoup" }
concurrent-linked-hashmap = { module = "com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru", version.ref = "concurrent-linked-hashmap" }
spring-security-crypto = { module = "org.springframework.security:spring-security-crypto", version.ref = "spring-security" }
hibernate-validator = { module = "org.hibernate.validator:hibernate-validator", version.ref = "hibernate-validator" }
reflections = { module = "org.reflections:reflections", version.ref = "reflections" }
tika-core = { module = "org.apache.tika:tika-core", version.ref = "tika" }
bouncycastle-provider = { module = "org.bouncycastle:bcprov-jdk18on", version.ref = "bouncycastle" }

[bundles]
# BOM 组合 - 便于统一导入
spring-boms = ["spring-boot-bom", "spring-framework-bom", "spring-ai-bom"]

# Spring AI 核心组合
spring-ai-core-bundle = ["spring-ai-openai", "spring-ai-core"]
