import org.gradle.api.artifacts.VersionCatalogsExtension
import org.gradle.kotlin.dsl.getByType

plugins {
    `java-library`
    id("com.diffplug.spotless")
}

val libs = extensions.getByType<VersionCatalogsExtension>().named("libs")

dependencies {
    // 导入 BOM 平台，统一版本管理
    implementation(platform(libs.findLibrary("spring-boot-bom").get()))
    implementation(platform(libs.findLibrary("spring-framework-bom").get()))
    // Spring AI BOM 暂时注释，先确保基础 BOM 配置正常
    // implementation(platform(libs.findLibrary("spring-ai-bom").get()))

    compileOnly(libs.findLibrary("lombok").get())
    annotationProcessor(libs.findLibrary("lombok").get())
    testImplementation(libs.findLibrary("spring-boot-starter-testing").get())
    testCompileOnly(libs.findLibrary("lombok").get())
    testAnnotationProcessor(libs.findLibrary("lombok").get())
    testRuntimeOnly(libs.findLibrary("h2").get())
}

tasks.withType<JavaCompile> {
    options.isIncremental = true
}

spotless {
    java {
        removeUnusedImports()
    }
} 