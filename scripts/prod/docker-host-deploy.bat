@echo off
echo ax-admin Host网络模式部署脚本
echo ================================

REM 检查PostgreSQL连接
echo 检查PostgreSQL连接...
pg_isready -h localhost -p 5432 -U txy >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ PostgreSQL连接正常
) else (
    echo ❌ PostgreSQL连接失败，请确保PostgreSQL服务已启动
    pause
    exit /b 1
)

REM 检查Redis连接
echo 检查Redis连接...
redis-cli -h localhost -p 6379 ping | findstr PONG >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Redis连接正常
) else (
    echo ❌ Redis连接失败，请确保Redis服务已启动
    pause
    exit /b 1
)

REM 构建应用
echo 构建ax-admin应用...
call %~dp0..\gradlew.bat clean build -x test

if %errorlevel% equ 0 (
    echo ✅ 应用构建成功
) else (
    echo ❌ 应用构建失败
    pause
    exit /b 1
)

REM 切换到.docker目录进行Docker操作
cd /d %~dp0..\..\..\.docker

REM 构建Docker镜像
echo 构建Docker镜像...
docker-compose build

REM 启动应用（host网络模式）
echo 启动ax-admin应用（host网络模式）...
docker-compose up -d

REM 检查应用状态
echo 等待应用启动...
timeout /t 10 /nobreak >nul

curl -s http://localhost:10245/actuator/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ ax-admin应用启动成功！
    echo 🌐 访问地址: http://localhost:10245
    echo 📚 API文档: http://localhost:10245/doc.html
) else (
    echo ❌ 应用启动失败，请检查日志
    docker-compose logs erp-api
)

pause 