@echo off
echo 🔨 构建并部署ax-admin生产环境

echo 🧹 清理旧构建...
call %~dp0..\gradlew.bat clean

echo 🚀 构建生产JAR包...
call %~dp0..\gradlew.bat :ax-admin:bootJar -x test

if %errorlevel% equ 0 (
    echo ✅ 构建成功！
    echo 📦 JAR包位置: ax-admin\build\libs\ax-admin-3.0.0.jar
    echo.
    echo 🚀 部署命令:
    echo java -jar -Dspring.profiles.active=prod ax-admin\build\libs\ax-admin-3.0.0.jar
) else (
    echo ❌ 构建失败！
)

pause 